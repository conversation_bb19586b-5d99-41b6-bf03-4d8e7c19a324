#include <jni.h>
#include <android/native_window_jni.h>
#include <EGL/egl.h>
#include <GLES2/gl2.h>
#include <vector>
#include <thread>
#include <chrono>
#include <optional>
#include <random>
#include <string>
#include <algorithm> // For std::erase_if

#include <android/log.h> // For logging
#include <game-activity/native_app_glue/android_native_app_glue.h> // For AMOTION_EVENT_ACTION_*

#define LOG_TAG "CarRacer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// --- Utility Structures ---
struct Vec2 {
    float x = 0.0f, y = 0.0f;
};

struct RectF {
    float left = 0.0f, top = 0.0f, right = 0.0f, bottom = 0.0f;

    // C++17: In-class member initialization
    RectF() = default;
    constexpr RectF(float l, float t, float r, float b) : left(l), top(t), right(r), bottom(b) {}

    static bool intersects(const RectF& a, const RectF& b) {
        return a.left < b.right && a.right > b.left &&
               a.top < b.bottom && a.bottom > b.top;
    }
};

// --- Game Entities ---
struct PlayerCar {
    Vec2 position;
    Vec2 size {150.0f, 250.0f}; // Default size
    RectF getCollisionRect() const {
        return RectF(position.x - size.x / 2, position.y - size.y / 2,
                     position.x + size.x / 2, position.y + size.y / 2);
    }
};

struct EnemyCar {
    Vec2 position;
    Vec2 size {150.0f, 250.0f};
    float speed;
    RectF getCollisionRect() const {
        return RectF(position.x - size.x / 2, position.y - size.y / 2,
                     position.x + size.x / 2, position.y + size.y / 2);
    }
};

// --- Game State ---
struct GameState {
    PlayerCar player;
    std::vector<EnemyCar> enemies;
    std::vector<RectF> roadStripes;
    int score = 0;
    bool isGameOver = false;
    float screenWidth = 0.0f, screenHeight = 0.0f;

    // C++17: Modern random number generation
    std::mt19937 rng{std::random_device{}()};
    std::uniform_real_distribution<float> enemyXDist;
    std::uniform_real_distribution<float> enemySpeedDist;
};

std::optional<GameState> gameState; // Game state stored as optional

// --- OpenGL ES 2.0 Globals ---
EGLDisplay gDisplay = EGL_NO_DISPLAY;
EGLSurface gSurface = EGL_NO_SURFACE;
EGLContext gContext = EGL_NO_CONTEXT;
ANativeWindow* gWindow = nullptr;

GLuint gProgram = 0;
GLint gPositionLoc = -1;
GLint gColorLoc = -1;
GLint gMatrixLoc = -1;

// Simple shaders for 2D rendering
const char* vertexShaderSource = R"(
attribute vec2 a_position;
attribute vec4 a_color;
uniform mat4 u_matrix;
varying vec4 v_color;
void main() {
    gl_Position = u_matrix * vec4(a_position, 0.0, 1.0);
    v_color = a_color;
}
)";

const char* fragmentShaderSource = R"(
precision mediump float;
varying vec4 v_color;
void main() {
    gl_FragColor = v_color;
}
)";

// --- Game Loop Variables ---
std::thread gameThread;
bool gameRunning = false;
std::chrono::high_resolution_clock::time_point lastFrameTime;

// --- Function Declarations ---
void init_gl();
void shutdown_gl();
GLuint loadShader(GLenum type, const char* source);
GLuint createProgram(const char* vertexSource, const char* fragmentSource);
void drawRect(const RectF& rect, const Vec2& screen_size, float r, float g, float b, float a);
void drawText(const std::string& text, float x, float y, float scale, const Vec2& screen_size, float r, float g, float b, float a);

void init_game(float width, float height);
void update_game_state(float deltaTime);
void render_frame();
void game_loop();

// --- JNI Functions ---
extern "C" {

JNIEXPORT void JNICALL
Java_com_example_carracer_MainActivity_onTouchEvent(JNIEnv* env, jobject thiz, jint action, jfloat x, jfloat y) {
    if (!gameState) {
        return;
    }

    if (gameState->isGameOver) {
        // Restart game on touch if game over
        init_game(gameState->screenWidth, gameState->screenHeight);
        return;
    }

    // Move player car based on touch X coordinate
    if (action == AMOTION_EVENT_ACTION_MOVE || action == AMOTION_EVENT_ACTION_DOWN) {
        gameState->player.position.x = x;
    }
}

// --- Android Native App Glue Callbacks ---
void handle_cmd(android_app *pApp, int32_t cmd) {
    switch (cmd) {
        case APP_CMD_INIT_WINDOW:
            LOGI("APP_CMD_INIT_WINDOW");
            gWindow = pApp->window;
            init_gl();
            // Get screen dimensions after GL context is created
            if (gDisplay != EGL_NO_DISPLAY && gSurface != EGL_NO_SURFACE) {
                EGLint width, height;
                eglQuerySurface(gDisplay, gSurface, EGL_WIDTH, &width);
                eglQuerySurface(gDisplay, gSurface, EGL_HEIGHT, &height);
                init_game(static_cast<float>(width), static_cast<float>(height));
                gameRunning = true;
                gameThread = std::thread(game_loop);
            }
            break;
        case APP_CMD_TERM_WINDOW:
            LOGI("APP_CMD_TERM_WINDOW");
            gameRunning = false;
            if (gameThread.joinable()) {
                gameThread.join();
            }
            shutdown_gl();
            gWindow = nullptr;
            gameState.reset(); // Clear game state
            break;
        case APP_CMD_GAINED_FOCUS:
            LOGI("APP_CMD_GAINED_FOCUS");
            // Resume game loop if paused
            if (gWindow && !gameRunning) {
                gameRunning = true;
                gameThread = std::thread(game_loop);
            }
            break;
        case APP_CMD_LOST_FOCUS:
            LOGI("APP_CMD_LOST_FOCUS");
            // Pause game loop
            gameRunning = false;
            if (gameThread.joinable()) {
                gameThread.join();
            }
            break;
        default:
            break;
    }
}

void android_main(struct android_app *pApp) {
    LOGI("Welcome to android_main");
    pApp->onAppCmd = handle_cmd;

    // This sets up a typical game/event loop. It will run until the app is destroyed.
    do {
        int events;
        android_poll_source *pSource;
        // Process all pending events before running game logic.
        // 0 is non-blocking.
        while (ALooper_pollOnce(0, nullptr, &events, reinterpret_cast<void**>(&pSource)) >= 0) {
            if (pSource) {
                pSource->process(pApp, pSource);
            }
        }
    } while (!pApp->destroyRequested);
}

} // extern "C"

// --- OpenGL ES 2.0 Implementation ---
void init_gl() {
    gDisplay = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    eglInitialize(gDisplay, nullptr, nullptr);

    const EGLint attribs[] = {
            EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
            EGL_SURFACE_TYPE, EGL_WINDOW_BIT,
            EGL_BLUE_SIZE, 8,
            EGL_GREEN_SIZE, 8,
            EGL_RED_SIZE, 8,
            EGL_DEPTH_SIZE, 16, // ES 2.0 typically uses 16-bit depth
            EGL_NONE
    };
    EGLint numConfigs;
    EGLConfig config;
    eglChooseConfig(gDisplay, attribs, &config, 1, &numConfigs);

    EGLint format;
    eglGetConfigAttrib(gDisplay, config, EGL_NATIVE_VISUAL_ID, &format);
    ANativeWindow_setBuffersGeometry(gWindow, 0, 0, format);
    gSurface = eglCreateWindowSurface(gDisplay, config, gWindow, nullptr);

    const EGLint contextAttribs[] = {
            EGL_CONTEXT_CLIENT_VERSION, 2,
            EGL_NONE
    };
    gContext = eglCreateContext(gDisplay, config, EGL_NO_CONTEXT, contextAttribs);

    if (eglMakeCurrent(gDisplay, gSurface, gSurface, gContext) == EGL_FALSE) {
        LOGE("Failed to make current EGL context");
        return;
    }

    gProgram = createProgram(vertexShaderSource, fragmentShaderSource);
    if (gProgram == 0) {
        LOGE("Failed to create shader program");
        return;
    }
    glUseProgram(gProgram);

    gPositionLoc = glGetAttribLocation(gProgram, "a_position");
    gColorLoc = glGetAttribLocation(gProgram, "a_color");
    gMatrixLoc = glGetUniformLocation(gProgram, "u_matrix");

    glClearColor(0.2f, 0.7f, 0.2f, 1.0f); // Green background for road
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
}

void shutdown_gl() {
    if (gDisplay != EGL_NO_DISPLAY) {
        eglMakeCurrent(gDisplay, EGL_NO_SURFACE, EGL_NO_SURFACE, EGL_NO_CONTEXT);
        if (gContext != EGL_NO_CONTEXT) {
            eglDestroyContext(gDisplay, gContext);
            gContext = EGL_NO_CONTEXT;
        }
        if (gSurface != EGL_NO_SURFACE) {
            eglDestroySurface(gDisplay, gSurface);
            gSurface = EGL_NO_SURFACE;
        }
        eglTerminate(gDisplay);
        gDisplay = EGL_NO_DISPLAY;
    }
    if (gProgram) {
        glDeleteProgram(gProgram);
        gProgram = 0;
    }
}

GLuint loadShader(GLenum type, const char* source) {
    GLuint shader = glCreateShader(type);
    glShaderSource(shader, 1, &source, nullptr);
    glCompileShader(shader);

    GLint compiled;
    glGetShaderiv(shader, GL_COMPILE_STATUS, &compiled);
    if (!compiled) {
        GLint infoLen = 0;
        glGetShaderiv(shader, GL_INFO_LOG_LENGTH, &infoLen);
        if (infoLen > 1) {
            std::vector<char> infoLog(infoLen);
            glGetShaderInfoLog(shader, infoLen, nullptr, infoLog.data());
            LOGE("Error compiling shader:\n%s", infoLog.data());
        }
        glDeleteShader(shader);
        return 0;
    }
    return shader;
}

GLuint createProgram(const char* vertexSource, const char* fragmentSource) {
    GLuint vertexShader = loadShader(GL_VERTEX_SHADER, vertexSource);
    if (vertexShader == 0) return 0;

    GLuint fragmentShader = loadShader(GL_FRAGMENT_SHADER, fragmentSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        return 0;
    }

    GLuint program = glCreateProgram();
    glAttachShader(program, vertexShader);
    glAttachShader(program, fragmentShader);
    glLinkProgram(program);

    GLint linked;
    glGetProgramiv(program, GL_LINK_STATUS, &linked);
    if (!linked) {
        GLint infoLen = 0;
        glGetProgramiv(program, GL_INFO_LOG_LENGTH, &infoLen);
        if (infoLen > 1) {
            std::vector<char> infoLog(infoLen);
            glGetProgramInfoLog(program, infoLen, nullptr, infoLog.data());
            LOGE("Error linking program:\n%s", infoLog.data());
        }
        glDeleteProgram(program);
        glDeleteShader(vertexShader);
        glDeleteShader(fragmentShader);
        return 0;
    }

    glDeleteShader(vertexShader);
    glDeleteShader(fragmentShader);
    return program;
}

void drawRect(const RectF& rect, const Vec2& screen_size, float r, float g, float b, float a) {
    // Normalize coordinates to OpenGL range [-1, 1]
    float x1 = (rect.left / screen_size.x) * 2.0f - 1.0f;
    float y1 = 1.0f - (rect.top / screen_size.y) * 2.0f;
    float x2 = (rect.right / screen_size.x) * 2.0f - 1.0f;
    float y2 = 1.0f - (rect.bottom / screen_size.y) * 2.0f;

    GLfloat vertices[] = {
        x1, y1, // Top-left
        x2, y1, // Top-right
        x1, y2, // Bottom-left
        x2, y2  // Bottom-right
    };

    GLfloat colors[] = {
        r, g, b, a,
        r, g, b, a,
        r, g, b, a,
        r, g, b, a
    };

    glEnableVertexAttribArray(gPositionLoc);
    glVertexAttribPointer(gPositionLoc, 2, GL_FLOAT, GL_FALSE, 0, vertices);

    glEnableVertexAttribArray(gColorLoc);
    glVertexAttribPointer(gColorLoc, 4, GL_FLOAT, GL_FALSE, 0, colors);

    // Identity matrix for 2D orthographic projection (already normalized)
    float matrix[16];
    // C++17: if constexpr for compile-time branching
    if constexpr (true) { // Always true for this 2D setup, but demonstrates usage
        // Orthographic projection is implicitly handled by normalizing coordinates to [-1, 1]
        // and using an identity matrix.
        // For a more general 2D orthographic projection, you'd use a matrix like:
        // Utility::buildOrthographicMatrix(matrix, halfHeight, aspect, near, far);
        // But since we normalize vertices directly, an identity matrix works for the shader.
        matrix[0] = 1.0f; matrix[1] = 0.0f; matrix[2] = 0.0f; matrix[3] = 0.0f;
        matrix[4] = 0.0f; matrix[5] = 1.0f; matrix[6] = 0.0f; matrix[7] = 0.0f;
        matrix[8] = 0.0f; matrix[9] = 0.0f; matrix[10] = 1.0f; matrix[11] = 0.0f;
        matrix[12] = 0.0f; matrix[13] = 0.0f; matrix[14] = 0.0f; matrix[15] = 1.0f;
    }
    glUniformMatrix4fv(gMatrixLoc, 1, GL_FALSE, matrix);

    glDrawArrays(GL_TRIANGLE_STRIP, 0, 4);

    glDisableVertexAttribArray(gPositionLoc);
    glDisableVertexAttribArray(gColorLoc);
}

void drawText(const std::string& text, float x, float y, float scale, const Vec2& screen_size, float r, float g, float b, float a) {
    // This is a placeholder for actual text rendering.
    // Real text rendering requires font loading, texture atlases, and more complex shaders.
    // For now, we'll just log the text to show it's being "drawn".
    LOGI("Drawing text: %s at (%f, %f) with scale %f", text.c_str(), x, y, scale);

    // As a visual cue for text, we can draw a small colored rectangle.
    // This is NOT actual text rendering.
    float text_rect_width = 200.0f * scale; // Placeholder size
    float text_rect_height = 50.0f * scale;
    RectF textRect(x - text_rect_width / 2, y - text_rect_height / 2,
                   x + text_rect_width / 2, y + text_rect_height / 2);
    drawRect(textRect, screen_size, r, g, b, a);
}

// --- Game Logic Implementation ---
void init_game(float width, float height) {
    gameState.emplace(); // C++17: Construct GameState in-place
    gameState->screenWidth = width;
    gameState->screenHeight = height;
    gameState->score = 0;
    gameState->isGameOver = false;

    // Initialize player car position (bottom center of screen)
    gameState->player.position = {width / 2.0f, height - gameState->player.size.y / 2 - 50.0f};

    // Initialize road stripes
    gameState->roadStripes.clear();
    float stripeHeight = 100.0f;
    float stripeGap = 150.0f;
    float stripeWidth = 50.0f;
    for (int i = 0; i < 5; ++i) {
        gameState->roadStripes.emplace_back(
            width / 2.0f - stripeWidth / 2, // left
            i * (stripeHeight + stripeGap), // top
            width / 2.0f + stripeWidth / 2, // right
            i * (stripeHeight + stripeGap) + stripeHeight // bottom
        );
    }

    // Initialize enemy distribution
    gameState->enemyXDist = std::uniform_real_distribution<float>(
        gameState->player.size.x / 2, gameState->screenWidth - gameState->player.size.x / 2
    );
    gameState->enemySpeedDist = std::uniform_real_distribution<float>(300.0f, 600.0f);

    gameState->enemies.clear();
    LOGI("Game Initialized. Screen: %fx%f", width, height);
}

void update_game_state(float deltaTime) {
    // C++17: Optional check
    if (!gameState || gameState->isGameOver) {
        return;
    }

    // Move road stripes
    float roadSpeed = 500.0f; // Pixels per second
    for (auto& stripe : gameState->roadStripes) {
        stripe.top += roadSpeed * deltaTime;
        stripe.bottom += roadSpeed * deltaTime;
    }

    // Recycle road stripes that go off-screen
    // C++17: std::erase_if for efficient removal
    std::erase_if(gameState->roadStripes, [&](const RectF& stripe) {
        return stripe.top > gameState->screenHeight;
    });

    // Add new road stripes if needed
    if (gameState->roadStripes.empty() || gameState->roadStripes.back().bottom < gameState->screenHeight) {
        float stripeHeight = 100.0f;
        float stripeGap = 150.0f;
        float stripeWidth = 50.0f;
        float newStripeY = gameState->roadStripes.empty() ? 0 : gameState->roadStripes.back().bottom - gameState->screenHeight - stripeGap;
        gameState->roadStripes.emplace_back(
            gameState->screenWidth / 2.0f - stripeWidth / 2,
            newStripeY,
            gameState->screenWidth / 2.0f + stripeWidth / 2,
            newStripeY + stripeHeight
        );
    }

    // Move enemy cars
    for (auto& enemy : gameState->enemies) {
        enemy.position.y += enemy.speed * deltaTime;
    }

    // Spawn new enemy cars
    // Simple spawning logic: if no enemies or last enemy is far enough, spawn a new one
    if (gameState->enemies.empty() || gameState->enemies.back().position.y > gameState->screenHeight * 0.75f) {
        EnemyCar newEnemy;
        newEnemy.position.x = gameState->enemyXDist(gameState->rng);
        newEnemy.position.y = -newEnemy.size.y / 2; // Start above screen
        newEnemy.speed = gameState->enemySpeedDist(gameState->rng);
        gameState->enemies.push_back(newEnemy);
        LOGI("Spawned enemy at X: %f, Speed: %f", newEnemy.position.x, newEnemy.speed);
    }

    // Remove off-screen enemy cars and update score
    // C++17: structured bindings for easy access to enemy properties
    std::erase_if(gameState->enemies, [&](const EnemyCar& enemy) {
        if (enemy.position.y - enemy.size.y / 2 > gameState->screenHeight) {
            gameState->score += 10; // Score for passing an enemy
            LOGI("Enemy passed. Score: %d", gameState->score);
            return true;
        }
        return false;
    });

    // Collision Detection
    RectF playerRect = gameState->player.getCollisionRect();
    for (const auto& enemy : gameState->enemies) {
        RectF enemyRect = enemy.getCollisionRect();
        if (RectF::intersects(playerRect, enemyRect)) {
            gameState->isGameOver = true;
            LOGI("Game Over! Final Score: %d", gameState->score);
            break;
        }
    }
}

void render_frame() {
    if (!gameState) {
        return;
    }

    // Ensure GL context is current
    if (eglMakeCurrent(gDisplay, gSurface, gSurface, gContext) == EGL_FALSE) {
        LOGE("Failed to make current EGL context in render_frame");
        return;
    }

    glViewport(0, 0, static_cast<GLsizei>(gameState->screenWidth), static_cast<GLsizei>(gameState->screenHeight));
    glClear(GL_COLOR_BUFFER_BIT);

    // Draw road stripes
    for (const auto& stripe : gameState->roadStripes) {
        drawRect(stripe, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 1.0f, 1.0f, 1.0f); // White stripes
    }

    // Draw player car (red)
    RectF playerRenderRect = gameState->player.getCollisionRect();
    drawRect(playerRenderRect, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 0.0f, 0.0f, 1.0f);

    // Draw enemy cars (blue)
    for (const auto& enemy : gameState->enemies) {
        RectF enemyRenderRect = enemy.getCollisionRect();
        drawRect(enemyRenderRect, {gameState->screenWidth, gameState->screenHeight}, 0.0f, 0.0f, 1.0f, 1.0f);
    }

    // Draw score
    drawText("Score: " + std::to_string(gameState->score), 100.0f, 100.0f, 1.0f, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 1.0f, 0.0f, 1.0f);

    // Game Over Screen
    if (gameState->isGameOver) {
        // Darken screen
        drawRect(RectF(0, 0, gameState->screenWidth, gameState->screenHeight),
                 {gameState->screenWidth, gameState->screenHeight}, 0.0f, 0.0f, 0.0f, 0.7f); // Semi-transparent black

        drawText("GAME OVER", gameState->screenWidth / 2.0f, gameState->screenHeight / 2.0f - 50.0f, 2.0f, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 1.0f, 1.0f, 1.0f);
        drawText("Score: " + std::to_string(gameState->score), gameState->screenWidth / 2.0f, gameState->screenHeight / 2.0f + 50.0f, 1.5f, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 1.0f, 1.0f, 1.0f);
        drawText("Tap to Restart", gameState->screenWidth / 2.0f, gameState->screenHeight / 2.0f + 150.0f, 1.0f, {gameState->screenWidth, gameState->screenHeight}, 1.0f, 1.0f, 1.0f, 1.0f);
    }

    eglSwapBuffers(gDisplay, gSurface);
}

void game_loop() {
    lastFrameTime = std::chrono::high_resolution_clock::now();
    while (gameRunning) {
        // C++17: std::chrono for precise time management
        auto currentTime = std::chrono::high_resolution_clock::now();
        std::chrono::duration<float> deltaTime = currentTime - lastFrameTime;
        lastFrameTime = currentTime;

        update_game_state(deltaTime.count());
        render_frame();

        // Simple frame rate control (e.g., target 60 FPS)
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
    LOGI("Game loop stopped.");
}
