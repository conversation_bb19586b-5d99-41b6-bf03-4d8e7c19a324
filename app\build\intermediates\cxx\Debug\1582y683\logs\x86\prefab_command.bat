@echo off
"C:\\Program Files\\Android\\Android Studio\\jbr\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  x86 ^
  --os-version ^
  30 ^
  --stl ^
  c++_static ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging732587666398671775\\staged-cli-output" ^
  "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.13\\transforms\\ddba6dede0622acbbc9ad55a63223602\\transformed\\games-activity-1.2.2\\prefab"
