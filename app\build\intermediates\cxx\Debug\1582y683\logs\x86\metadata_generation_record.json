[{"level_": 0, "message_": "Start JSON generation. Platform version: 30 min SDK version: x86", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\.cxx\\Debug\\1582y683\\x86\\android_gradle_build.json due to:", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- a file changed", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "  - C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt (LAST_MODIFIED_CHANGED)", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Android\\\\Android Studio\\\\jbr\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\android_studio\\\\TheStream\\\\gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  x86 ^\n  --os-version ^\n  30 ^\n  --stl ^\n  c++_static ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging732587666398671775\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\android_studio\\\\TheStream\\\\gradle\\\\caches\\\\8.13\\\\transforms\\\\ddba6dede0622acbbc9ad55a63223602\\\\transformed\\\\games-activity-1.2.2\\\\prefab\"\n", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "keeping json folder 'C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\.cxx\\Debug\\1582y683\\x86' but regenerating project", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=30\" ^\n  \"-DANDROID_PLATFORM=android-30\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1582y683\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1582y683\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\.cxx\\\\Debug\\\\1582y683\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\.cxx\\\\Debug\\\\1582y683\\\\x86\" ^\n  -GNinja\n", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HC:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=30\" ^\n  \"-DANDROID_PLATFORM=android-30\" ^\n  \"-DANDROID_ABI=x86\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=x86\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.0.12077973\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_CXX_FLAGS=-std=c++17\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1582y683\\\\obj\\\\x86\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\1582y683\\\\obj\\\\x86\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=C:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\.cxx\\\\Debug\\\\1582y683\\\\prefab\\\\x86\\\\prefab\" ^\n  \"-BC:\\\\Users\\\\<USER>\\\\Music\\\\remote\\\\CarRacer\\\\app\\\\.cxx\\\\Debug\\\\1582y683\\\\x86\" ^\n  -GNinja\n", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\.cxx\\Debug\\1582y683\\x86\\compile_commands.json.bin normally", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\.cxx\\Debug\\1582y683\\x86\\compile_commands.json to C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\.cxx\\tools\\debug\\x86\\compile_commands.json", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\Users\\<USER>\\Music\\remote\\CarRacer\\app\\src\\main\\cpp\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]