# C/C++ build system timings
generate_cxx_metadata
  [gap of 58ms]
  create-invalidation-state 132ms
  generate-prefab-packages
    [gap of 26ms]
    exec-prefab 1899ms
    [gap of 22ms]
  generate-prefab-packages completed in 1947ms
  execute-generate-process
    exec-configure 806ms
    [gap of 163ms]
  execute-generate-process completed in 970ms
  [gap of 75ms]
  write-metadata-json-to-file 30ms
generate_cxx_metadata completed in 3227ms

