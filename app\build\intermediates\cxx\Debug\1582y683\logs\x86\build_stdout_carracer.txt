ninja: Entering directory `C:\Users\<USER>\Music\remote\CarRacer\app\.cxx\Debug\1582y683\x86'
[1/2] Building CXX object CMakeFiles/carracer.dir/main.cpp.o
FAILED: CMakeFiles/carracer.dir/main.cpp.o 
C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++.exe --target=i686-none-linux-android30 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dcarracer_EXPORTS -IC:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.0.12077973/sysroot/usr/include -isystem C:/Users/<USER>/Desktop/android_studio/TheStream/gradle/caches/8.13/transforms/ddba6dede0622acbbc9ad55a63223602/transformed/games-activity-1.2.2/prefab/modules/game-activity/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -std=c++17 -fno-limit-debug-info  -fPIC -std=c++17 -MD -MT CMakeFiles/carracer.dir/main.cpp.o -MF CMakeFiles\carracer.dir\main.cpp.o.d -o CMakeFiles/carracer.dir/main.cpp.o -c C:/Users/<USER>/Music/remote/CarRacer/app/src/main/cpp/main.cpp
C:/Users/<USER>/Music/remote/CarRacer/app/src/main/cpp/main.cpp:454:10: error: no member named 'erase_if' in namespace 'std'
  454 |     std::erase_if(gameState->roadStripes, [&](const RectF& stripe) {
      |     ~~~~~^
C:/Users/<USER>/Music/remote/CarRacer/app/src/main/cpp/main.cpp:490:10: error: no member named 'erase_if' in namespace 'std'
  490 |     std::erase_if(gameState->enemies, [&](const EnemyCar& enemy) {
      |     ~~~~~^
2 errors generated.
ninja: build stopped: subcommand failed.
